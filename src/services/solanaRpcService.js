/**
 * Solana RPC Service - 简化的 Solana RPC 调用
 * 提供直接的 API 函数用于与 Solana 网络交互
 */

import { Connection, PublicKey } from '@solana/web3.js';
import programIds from '../data/program-ids.json';

// ==================== 网络配置 ====================
// Helius API Key 配置
const HELIUS_API_KEY = import.meta.env.VITE_HELIUS_API_KEY;

// 网络类型枚举
const NETWORK_TYPES = {
  MAINNET: 'mainnet',
  DEVNET: 'devnet',
  LOCALNET: 'localnet'
};

// 当前网络设置 - 在这里手动切换网络
const CURRENT_NETWORK = NETWORK_TYPES.MAINNET; // 👈 修改这里来切换网络

// 构建带 API Key 的 RPC URL
const buildHeliusRpcUrl = (network) => {
  const baseUrl = `https://${network}.helius-rpc.com`;
  return HELIUS_API_KEY ? `${baseUrl}/?api-key=${HELIUS_API_KEY}` : baseUrl;
};

// 网络配置（简化版，不再依赖外部 API）
const NETWORK_CONFIGS = {
  [NETWORK_TYPES.MAINNET]: {
    name: 'Mainnet',
    rpcUrl: buildHeliusRpcUrl('mainnet'),
    cfxToken: programIds.tokens.CFX_TOKEN_MINT,
    stakeProgramId: import.meta.env.VITE_STAKE_PROGRAM_ID || programIds.programs.CFX_STAKE_CORE,
    requiresApiKey: false
  },
  [NETWORK_TYPES.DEVNET]: {
    name: 'Devnet', 
    rpcUrl: buildHeliusRpcUrl('devnet'),
    cfxToken: programIds.tokens.CFX_TOKEN_MINT,
    stakeProgramId: import.meta.env.VITE_STAKE_PROGRAM_ID_DEVNET || import.meta.env.VITE_STAKE_PROGRAM_ID || programIds.programs.CFX_STAKE_CORE,
    requiresApiKey: false
  },
  [NETWORK_TYPES.LOCALNET]: {
    name: 'Localnet',
    rpcUrl: 'http://127.0.0.1:8899',
    cfxToken: programIds.tokens.CFX_TOKEN_MINT,
    stakeProgramId: programIds.programs.CFX_STAKE_CORE,
    requiresApiKey: false
  }
};

// 获取当前网络配置（同步版本，向后兼容）
const getCurrentNetworkConfig = () => NETWORK_CONFIGS[CURRENT_NETWORK];

// 获取当前网络配置（异步版本，优先使用钱包 RPC）
const getCurrentNetworkConfigWithWallet = async () => {
  const baseConfig = NETWORK_CONFIGS[CURRENT_NETWORK];
  
  try {
    // 尝试获取钱包 RPC 端点
    const walletRpc = await getWalletRpcEndpoint();
    
    if (walletRpc) {
      // 如果有钱包 RPC，使用钱包的 RPC 端点
      return {
        ...baseConfig,
        rpcUrl: walletRpc,
        isUsingWalletRpc: true
      };
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('获取钱包 RPC 配置时出错，使用默认配置:', error);
    }
  }
  
  // 如果没有钱包 RPC 或获取失败，使用默认配置
  return {
    ...baseConfig,
    isUsingWalletRpc: false
  };
};

// 获取当前网络信息（同步版本，向后兼容）
const getCurrentNetwork = () => ({
  type: CURRENT_NETWORK,
  config: getCurrentNetworkConfig()
});

// 获取当前网络信息（异步版本，包含钱包 RPC 信息）
const getCurrentNetworkWithWallet = async () => ({
  type: CURRENT_NETWORK,
  config: await getCurrentNetworkConfigWithWallet()
});

// CFX Token 地址（根据网络动态获取）
const CFX_TOKEN_ADDRESS = getCurrentNetworkConfig().cfxToken;

// SPL Token 程序 ID - 从环境变量读取
const TOKEN_PROGRAM_ID = import.meta.env.VITE_TOKEN_PROGRAM_ID;

// ==================== 钱包 RPC 管理 ====================
// 用户钱包连接实例（统一使用钱包 RPC）
let walletConnection = null;
// 连接状态
let isWalletConnectionReady = false;
// 钱包 RPC 端点缓存
let walletRpcEndpoint = null;

/**
 * 获取用户钱包当前的 RPC 端点
 * @returns {Promise<string|null>} 钱包的 RPC 端点
 */
async function getWalletRpcEndpoint() {
  try {
    // 如果已有缓存且钱包仍连接，直接返回
    if (walletRpcEndpoint && window.solana?.isConnected) {
      return walletRpcEndpoint;
    }

    // 方法1: 检查 window.solana (Phantom 钱包)
    if (window.solana && window.solana.isConnected) {
      // 尝试获取 Phantom 钱包的连接信息
      if (window.solana.connection && window.solana.connection._rpcEndpoint) {
        const endpoint = window.solana.connection._rpcEndpoint;
        walletRpcEndpoint = endpoint; // 缓存
        if (import.meta.env.DEV) {
          console.log('🔗 获取到 Phantom 钱包 RPC:', endpoint);
        }
        return endpoint;
      }

      // 尝试通过 request 方法获取网络信息
      try {
        const network = await window.solana.request({ method: 'getChainId' });
        if (network) {
          const rpcEndpoint = mapNetworkToRpc(network);
          if (rpcEndpoint) {
            walletRpcEndpoint = rpcEndpoint; // 缓存
            if (import.meta.env.DEV) {
              console.log('🔗 通过网络 ID 推断 RPC:', network, '->', rpcEndpoint);
            }
            return rpcEndpoint;
          }
        }
      } catch (err) {
        // 忽略此方法的错误
      }

      // 尝试获取当前网络信息
      try {
        const cluster = await window.solana.request({ method: 'getNetwork' });
        if (cluster) {
          const rpcEndpoint = mapClusterToRpc(cluster);
          if (rpcEndpoint) {
            walletRpcEndpoint = rpcEndpoint; // 缓存
            if (import.meta.env.DEV) {
              console.log('🔗 通过集群信息推断 RPC:', cluster, '->', rpcEndpoint);
            }
            return rpcEndpoint;
          }
        }
      } catch (err) {
        // 忽略此方法的错误
      }
    }

    // 方法2: 检查其他钱包的 window 对象
    if (window.solflare && window.solflare.isConnected) {
      try {
        if (window.solflare.network) {
          const rpcEndpoint = mapClusterToRpc(window.solflare.network);
          if (rpcEndpoint) {
            walletRpcEndpoint = rpcEndpoint; // 缓存
            if (import.meta.env.DEV) {
              console.log('🔗 获取到 Solflare 钱包 RPC:', rpcEndpoint);
            }
            return rpcEndpoint;
          }
        }
      } catch (err) {
        // 忽略此方法的错误
      }
    }

    // 如果都无法获取，清除缓存并返回 null
    walletRpcEndpoint = null;
    return null;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('获取钱包 RPC 端点时出错:', error);
    }
    walletRpcEndpoint = null;
    return null;
  }
}

/**
 * 将网络 ID 映射到 RPC 端点
 * @param {string} networkId - 网络 ID
 * @returns {string|null} RPC 端点
 */
function mapNetworkToRpc(networkId) {
  const networkMap = {
    '101': buildHeliusRpcUrl('mainnet'),
    'mainnet-beta': buildHeliusRpcUrl('mainnet'),
    '102': 'https://api.testnet.solana.com',
    'testnet': 'https://api.testnet.solana.com',
    '103': buildHeliusRpcUrl('devnet'),
    'devnet': buildHeliusRpcUrl('devnet')
  };
  
  return networkMap[networkId] || null;
}

/**
 * 将集群名称映射到 RPC 端点
 * @param {string} cluster - 集群名称
 * @returns {string|null} RPC 端点
 */
function mapClusterToRpc(cluster) {
  const clusterMap = {
    'mainnet-beta': buildHeliusRpcUrl('mainnet'),
    'mainnet': buildHeliusRpcUrl('mainnet'),
    'testnet': 'https://api.testnet.solana.com',
    'devnet': buildHeliusRpcUrl('devnet'),
    'localnet': 'http://127.0.0.1:8899',
    'localhost': 'http://127.0.0.1:8899'
  };
  
  return clusterMap[cluster] || null;
}

/**
 * 初始化用户钱包 RPC 连接
 * @returns {Promise<boolean>} 初始化是否成功
 */
async function initializeWalletConnection() {
  try {
    // 动态导入避免循环依赖
    const { solanaWalletService } = await import('./solanaWalletService');

    // 检查钱包是否连接（宽容模式：未连接不算失败）
    if (!solanaWalletService.isWalletConnected()) {
      if (import.meta.env.DEV) {
        console.log('ℹ️ 钱包尚未连接，将在连接后自动启用钱包 RPC');
      }
      isWalletConnectionReady = false;
      return true; // 返回 true，不阻止服务初始化
    }

    // 获取钱包适配器
    const adapter = solanaWalletService.adapter;
    if (!adapter || !adapter.publicKey) {
      if (import.meta.env.DEV) {
        console.log('ℹ️ 钱包适配器尚未准备好，将在准备后自动启用');
      }
      isWalletConnectionReady = false;
      return true; // 返回 true，不阻止服务初始化
    }

    // 使用钱包 RPC 端点（如果可用）或默认配置
    const config = await getCurrentNetworkConfigWithWallet();
    const rpcEndpoint = config.rpcUrl;
    walletConnection = new Connection(rpcEndpoint, 'confirmed');

    if (import.meta.env.DEV) {
      if (config.isUsingWalletRpc) {
        console.log(`🔗 使用钱包 RPC 端点: ${rpcEndpoint}`);
      } else {
        console.log(`🔗 使用默认 RPC 端点: ${rpcEndpoint}`);
      }
    }

    // 测试连接（可选，失败不影响初始化）
    try {
      await walletConnection.getVersion();
      isWalletConnectionReady = true;

      if (import.meta.env.DEV) {
        console.log(`✅ 钱包 RPC 连接已就绪 (${adapter.name} -> ${rpcEndpoint})`);
      }

      return true;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('⚠️ 钱包 RPC 连接测试失败，但不影响初始化:', error.message);
      }
      // 即使测试失败，也保持连接实例，可能在实际使用时会成功
      isWalletConnectionReady = false;
      return true; // 返回 true，不阻止服务初始化
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('⚠️ 初始化钱包 RPC 连接时发生错误，但不影响服务启动:', error.message);
    }
    isWalletConnectionReady = false;
    walletConnection = null;
    return true; // 返回 true，不阻止服务初始化
  }
}

/**
 * 初始化钱包 RPC 服务
 * @returns {Promise<boolean>} 初始化是否成功
 */
async function initialize() {
  try {
    // 尝试初始化钱包连接（不影响服务启动）
    await initializeWalletConnection();

    if (import.meta.env.DEV) {
      console.log("✅ 钱包 RPC 服务初始化完成");
    }

    return true;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn("⚠️ 钱包 RPC 服务初始化时发生错误，但服务仍可启动:", error);
    }
    // 即使钱包初始化失败，服务本身也算初始化成功
    return true;
  }
}

/**
 * 获取钱包 RPC 连接实例
 * @param {string} method - RPC 方法名（保留参数以兼容现有代码）
 * @returns {Promise<Connection>} Solana 连接实例
 */
async function getConnection(method = null) {
  // 如果钱包连接未就绪，尝试重新初始化
  if (!isWalletConnectionReady) {
    await initializeWalletConnection();
  }

  // 必须使用钱包连接，如果不可用则抛出错误
  // if (!isWalletConnectionReady || !walletConnection) {
  //   throw new Error('钱包 RPC 连接不可用，请确保钱包已正确连接');
  // }

  return walletConnection;
}

/**
 * 刷新钱包连接（当钱包状态改变时调用）
 */
async function refreshWalletConnection() {
  isWalletConnectionReady = false;
  walletConnection = null;
  walletRpcEndpoint = null; // 清除缓存的 RPC 端点
  await initializeWalletConnection();
}

/**
 * 强制刷新钱包 RPC 端点（当用户在钱包中切换网络时调用）
 */
async function refreshWalletRpc() {
  try {
    // 清除缓存并重新获取钱包 RPC 端点
    walletRpcEndpoint = null;
    const newRpcEndpoint = await getWalletRpcEndpoint();
    
    // 如果获取到新的 RPC 端点，更新连接
    if (newRpcEndpoint) {
      walletConnection = new Connection(newRpcEndpoint, 'confirmed');
      
      // 测试新连接
      try {
        await walletConnection.getVersion();
        isWalletConnectionReady = true;
        
        if (import.meta.env.DEV) {
          console.log('🔄 钱包 RPC 已更新:', newRpcEndpoint);
        }
        
        return true;
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('⚠️ 新的钱包 RPC 连接测试失败:', error.message);
        }
        isWalletConnectionReady = false;
        return false;
      }
    } else {
      // 如果无法获取钱包 RPC，重新初始化使用默认配置
      await initializeWalletConnection();
      return true;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('刷新钱包 RPC 时出错:', error);
    }
    return false;
  }
}

/**
 * 获取当前使用的 RPC 端点信息
 */
async function getRpcEndpointInfo() {
  const config = await getCurrentNetworkConfigWithWallet();
  
  return {
    walletRpc: walletRpcEndpoint,
    defaultRpc: NETWORK_CONFIGS[CURRENT_NETWORK].rpcUrl,
    currentRpc: config.rpcUrl,
    isUsingWalletRpc: config.isUsingWalletRpc
  };
}

/**
 * 检测钱包真实连接状态（轻量级版本）
 * @param {boolean} skipRpcTest - 是否跳过 RPC 测试，默认为 true
 * @returns {Promise<boolean>} 钱包是否真实连接
 */
async function checkWalletRealConnection(skipRpcTest = true) {
  try {
    // 检查 window.solana 是否存在
    if (!window.solana) {
      return false;
    }

    // 检查钱包是否连接
    if (!window.solana.isConnected) {
      return false;
    }

    // 检查是否有公钥
    if (!window.solana.publicKey) {
      return false;
    }

    // 尝试获取钱包地址
    const address = window.solana.publicKey.toString();
    if (!address) {
      return false;
    }

    // 可选：进行轻量级的 RPC 调用验证（仅在必要时）
    if (!skipRpcTest && walletConnection) {
      try {
        // 使用超时机制，避免长时间等待
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('RPC 调用超时')), 3000)
        );
        
        const balancePromise = walletConnection.getBalance(window.solana.publicKey);
        
        await Promise.race([balancePromise, timeoutPromise]);
        return true;
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('钱包 RPC 连接测试失败（这是正常的）:', error.message);
        }
        // RPC 测试失败不代表钱包未连接，可能是网络问题
        return true; // 保守策略：认为钱包仍然连接
      }
    }

    // 基础检测通过，认为钱包已连接
    return true;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('检测钱包连接状态失败:', error);
    }
    return false;
  }
}

/**
 * 获取 RPC 连接状态
 */
function getConnectionStatus() {
  return {
    walletReady: isWalletConnectionReady,
    walletEndpoint: walletConnection?.rpcEndpoint || null
  };
}

/**
 * 执行 JSON-RPC 请求（带重试机制）
 * @param {string} method - RPC 方法名
 * @param {Array} params - RPC 参数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise<any>} - RPC 响应
 */
async function callJsonRpc(method, params = [], maxRetries = 3, retryDelay = 1000) {
  const config = await getCurrentNetworkConfigWithWallet();

  let lastError = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // 为本地网络添加特殊的 fetch 配置
      const fetchOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now() + Math.random(), // 使用唯一ID避免缓存问题
          method,
          params,
        }),
      };

      // 如果是本地网络，添加额外的配置
      if (CURRENT_NETWORK === NETWORK_TYPES.LOCALNET) {
        fetchOptions.mode = 'cors';
        fetchOptions.credentials = 'omit';
      }

      const response = await fetch(config.rpcUrl, fetchOptions);

      if (!response.ok) {
        const errorText = await response.text();

        // 如果是本地网络连接失败，提供更友好的错误信息
        if (CURRENT_NETWORK === NETWORK_TYPES.LOCALNET && response.status === 0) {
          throw new Error(`无法连接到本地 Solana RPC (${config.rpcUrl})。请确保：
1. Solana 测试验证器正在运行 (solana-test-validator)
2. RPC 端口 8899 未被占用
3. 防火墙允许本地连接`);
        }

        // 对于 403 和 429 错误，进行重试
        if ((response.status === 403 || response.status === 429) && attempt < maxRetries) {
          lastError = new Error(`HTTP error! status: ${response.status}, response: ${errorText}`);
          if (import.meta.env.DEV) {
            console.warn(`RPC ${method} attempt ${attempt + 1} failed with ${response.status}, retrying in ${retryDelay}ms...`);
          }
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1))); // 指数退避
          continue;
        }

        throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`);
      }

      const data = await response.json();

      if (data.error) {
        // 对于特定的 RPC 错误，也进行重试
        if (data.error.code === -32403 && attempt < maxRetries) {
          lastError = new Error(`RPC error: ${JSON.stringify(data.error)}`);
          if (import.meta.env.DEV) {
            console.warn(`RPC ${method} attempt ${attempt + 1} failed with error ${data.error.code}, retrying in ${retryDelay}ms...`);
          }
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
          continue;
        }
        throw new Error(`RPC error: ${JSON.stringify(data.error)}`);
      }

      // 检查结果是否存在
      if (data.result === undefined) {
        throw new Error(`RPC result is undefined for method: ${method}`);
      }

      // 对于 getBalance 方法，检查 value 字段
      if (method === 'getBalance' && data.result && typeof data.result === 'object') {
        // 如果结果是一个对象并且有 value 字段，返回 value
        if (data.result.value !== undefined) {
          return data.result.value;
        }
      }

      return data.result;
    } catch (error) {
      lastError = error;

      // 如果是最后一次尝试，或者是不可重试的错误，直接抛出
      if (attempt === maxRetries || !isRetryableError(error)) {
        if (import.meta.env.DEV) {
          console.error(`RPC call failed for method ${method} after ${attempt + 1} attempts:`, error);
        }
        throw error;
      }

      // 等待后重试
      if (import.meta.env.DEV) {
        console.warn(`RPC ${method} attempt ${attempt + 1} failed, retrying in ${retryDelay}ms...`, error.message);
      }
      await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
    }
  }

  throw lastError;
}

/**
 * 判断错误是否可以重试
 * @param {Error} error - 错误对象
 * @returns {boolean} - 是否可以重试
 */
function isRetryableError(error) {
  const errorMessage = error.message || '';

  // 网络相关错误可以重试
  if (errorMessage.includes('fetch') ||
      errorMessage.includes('network') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('HTTP error! status: 403') ||
      errorMessage.includes('HTTP error! status: 429') ||
      errorMessage.includes('Bad request, please try again later')) {
    return true;
  }

  // RPC 限流错误可以重试
  if (errorMessage.includes('-32403') || errorMessage.includes('-32429')) {
    return true;
  }

  return false;
}

/**
 * 获取钱包余额（使用钱包 RPC）
 * @param {string} address - 钱包地址
 * @returns {Promise<{success: boolean, balance?: number, error?: Error, message?: string}>} - 余额结果
 */
async function getBalance(address) {
  try {
    if (!address) {
      return {
        success: false,
        message: "未提供钱包地址"
      };
    }

    // 验证地址格式
    const publicKey = new PublicKey(address);

    // 使用钱包 RPC 获取余额
    const connection = await getConnection('getBalance');

    // 直接使用 Connection 对象调用
    const lamports = await connection.getBalance(publicKey);

    if (import.meta.env.DEV) {
      console.log(`💰 余额查询 [WALLET]: ${address.substring(0, 8)}... = ${lamports} lamports`);
    }

    // 检查 lamports 是否为有效数值
    if (lamports === null || lamports === undefined || isNaN(lamports)) {
      return {
        success: false,
        message: "获取到的余额不是有效数值"
      };
    }

    // 将 lamports 转换为 SOL (1 SOL = 10^9 lamports)
    const solBalance = lamports / 1000000000;

    // 再次检查转换后的值是否有效
    if (isNaN(solBalance)) {
      return {
        success: false,
        message: "余额转换失败"
      };
    }

    return {
      success: true,
      balance: solBalance,
      rpcSource: 'wallet'
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("获取余额失败:", error);
    }
    return {
      success: false,
      error,
      message: error.message || "获取钱包余额失败"
    };
  }
}

/**
 * 获取账户信息
 * @param {string} address - 账户地址
 * @returns {Promise<{success: boolean, result?: Object, error?: Error, message?: string}>} - 账户信息结果
 */
async function getAccountInfo(address) {
  try {
    if (!address) {
      return {
        success: false,
        message: "未提供账户地址"
      };
    }

    // 验证地址格式
    const publicKey = new PublicKey(address);

    // 调用 getAccountInfo RPC 方法
    const result = await callJsonRpc('getAccountInfo', [publicKey.toString(), { encoding: 'jsonParsed' }]);

    return {
      success: true,
      result
    };
  } catch (error) {
    // 只在开发环境中输出详细错误
    if (import.meta.env.DEV) {
      console.error("获取账户信息失败:", error);
    }
    return {
      success: false,
      error,
      message: error.message || "获取账户信息失败"
    };
  }
}

/**
 * 获取交易信息
 * @param {string} signature - 交易签名
 * @returns {Promise<{success: boolean, result?: Object, error?: Error, message?: string}>} - 交易信息结果
 */
async function getTransaction(signature) {
  try {
    if (!signature) {
      return {
        success: false,
        message: "未提供交易签名"
      };
    }

    const result = await callJsonRpc('getTransaction', [signature, { encoding: 'jsonParsed' }]);

    return {
      success: true,
      result
    };
  } catch (error) {
    // 只在开发环境中输出详细错误
    if (import.meta.env.DEV) {
      console.error("获取交易信息失败:", error);
    }
    return {
      success: false,
      error,
      message: error.message || "获取交易信息失败"
    };
  }
}

/**
 * 获取用户的 CFX 代币余额（使用钱包 RPC）
 * @param {string} ownerAddress - 钱包地址
 * @returns {Promise<{success: boolean, balance?: number, error?: Error, message?: string}>} - CFX 代币余额结果
 */
async function getCfxTokenBalance(ownerAddress) {
  try {
    if (!ownerAddress) {
      return {
        success: false,
        message: "未提供钱包地址"
      };
    }

    if (!CFX_TOKEN_ADDRESS) {
      return {
        success: false,
        message: "未配置 CFX 代币地址。请在 .env 文件中添加 VITE_CFX_TOKEN"
      };
    }

    // 验证地址格式
    const publicKey = new PublicKey(ownerAddress);

    // 使用钱包 RPC 获取代币账户
    const connection = await getConnection('getTokenAccountsByOwner');

    // 第一步：获取用户的所有代币账户
    const tokenAccountsResult = await connection.getParsedTokenAccountsByOwner(
      publicKey,
      {
        programId: new PublicKey(TOKEN_PROGRAM_ID)
      }
    );

    if (import.meta.env.DEV) {
      console.log(`🪙 CFX 代币查询 [WALLET]: ${ownerAddress.substring(0, 8)}...`);
    }

    if (!tokenAccountsResult || !tokenAccountsResult.value || !Array.isArray(tokenAccountsResult.value)) {
      return {
        success: false,
        message: "获取代币账户失败：无效的响应格式"
      };
    }

    // 第二步：查找 CFX 代币账户
    const cfxTokenAccounts = tokenAccountsResult.value.filter(item => {
      try {
        const parsedData = item.account.data.parsed;
        return parsedData.info.mint === CFX_TOKEN_ADDRESS;
      } catch (e) {
        return false;
      }
    });

    // 如果没有找到 CFX 代币账户，返回余额为 0
    if (cfxTokenAccounts.length === 0) {
      return {
        success: true,
        balance: 0,
        message: "用户没有 CFX 代币账户",
        rpcSource: 'wallet'
      };
    }

    // 第三步：获取 CFX 代币余额
    const cfxTokenAccount = cfxTokenAccounts[0];
    const tokenAccountAddress = new PublicKey(cfxTokenAccount.pubkey);

    // 使用 getTokenAccountBalance 方法
    const balanceResult = await connection.getTokenAccountBalance(tokenAccountAddress);

    if (!balanceResult || !balanceResult.value) {
      return {
        success: false,
        message: "获取 CFX 代币余额失败：无效的响应格式"
      };
    }

    // 解析余额
    const { amount, decimals, uiAmount, uiAmountString } = balanceResult.value;

    return {
      success: true,
      balance: uiAmount,
      rawAmount: amount,
      decimals: decimals,
      uiAmountString: uiAmountString,
      rpcSource: 'wallet'
    };
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("获取 CFX 代币余额失败:", error);
    }
    return {
      success: false,
      error,
      message: error.message || "获取 CFX 代币余额失败"
    };
  }
}

/**
 * 获取网络状态信息
 * @returns {Promise<{success: boolean, networkInfo?: Object, error?: Error}>}
 */
async function getNetworkInfo() {
  try {
    const config = await getCurrentNetworkConfigWithWallet();
    const connection = await getConnection();

    if (!connection) {
      return {
        success: false,
        error: new Error('连接未初始化')
      };
    }

    const version = await connection.getVersion();
    const slot = await connection.getSlot();

    return {
      success: true,
      networkInfo: {
        network: config.name,
        networkType: CURRENT_NETWORK,
        rpcUrl: config.rpcUrl,
        isUsingWalletRpc: config.isUsingWalletRpc,
        version,
        currentSlot: slot,
        cfxTokenAddress: CFX_TOKEN_ADDRESS,
        stakeProgramId: config.stakeProgramId,
        requiresApiKey: config.requiresApiKey,
        // 如果是本地网络，包含 program-ids.json 中的额外信息
        ...(CURRENT_NETWORK === NETWORK_TYPES.LOCALNET && {
          deployedAccounts: programIds.deployed_accounts,
          metadata: programIds.metadata
        })
      }
    };
  } catch (error) {
    return {
      success: false,
      error
    };
  }
}

// 导出所有函数
const solanaRpcService = {
  initialize,
  getConnection,
  getBalance,
  getAccountInfo,
  getTransaction,
  getCfxTokenBalance,
  getNetworkInfo,
  getCurrentNetwork,
  getCurrentNetworkWithWallet,
  getCurrentNetworkConfigWithWallet,
  getWalletRpcEndpoint,
  refreshWalletConnection,
  refreshWalletRpc,
  getRpcEndpointInfo,
  getConnectionStatus,
  checkWalletRealConnection
};

export {
  getBalance,
  getAccountInfo,
  getTransaction,
  getCfxTokenBalance,
  getNetworkInfo,
  getCurrentNetwork,
  getCurrentNetworkWithWallet,
  getCurrentNetworkConfigWithWallet,
  getWalletRpcEndpoint,
  refreshWalletConnection,
  refreshWalletRpc,
  getRpcEndpointInfo,
  getConnectionStatus,
  checkWalletRealConnection,
  NETWORK_TYPES
};
export default solanaRpcService;

